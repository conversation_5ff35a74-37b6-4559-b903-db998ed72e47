<template>
  <el-dialog
    v-model="visible"
    title="用户详情"
    width="800px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <!-- 基本信息部分 -->
      <div class="basic-info-section">
        <h3 class="section-title">基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户头像">
            <el-avatar :size="60" :src="data.avatar_url" />
          </el-descriptions-item>
          <el-descriptions-item label="昵称">
            {{ data.nickname }}
          </el-descriptions-item>
          <el-descriptions-item label="OpenID">
            {{ data.openid }}
          </el-descriptions-item>
          <el-descriptions-item label="UnionID">
            {{ data.unionid || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="冥想等级">
            {{ data.meditation_level }}级 - {{ data.levelName }}
          </el-descriptions-item>
          <el-descriptions-item label="连续天数">
            {{ data.streak_days }}天
          </el-descriptions-item>
          <el-descriptions-item label="多肉数量">
            {{ data.plant_count || 0 }}个
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDate(data.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(data.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 统计数据部分 -->
      <div class="statistics-section">
        <h3 class="section-title">统计数据</h3>
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        <div v-else-if="statistics">
          <el-descriptions :column="2" border class="mb-4">
            <el-descriptions-item label="多肉数量">
              {{ statistics.plant_count }}个
            </el-descriptions-item>
            <el-descriptions-item label="收藏数量">
              {{ statistics.favorite_count }}个
            </el-descriptions-item>
          </el-descriptions>
          <el-divider content-position="left">最近30天冥想记录</el-divider>
          <el-table :data="statistics.meditation_stats" stripe>
            <el-table-column prop="period_date" label="日期" width="120" />
            <el-table-column
              prop="meditation_duration"
              label="冥想时长(秒)"
              width="120"
            />
            <el-table-column
              prop="energy_gained"
              label="获得能量"
              width="100"
            />
            <el-table-column
              prop="tasks_completed"
              label="完成任务"
              width="100"
            />
            <el-table-column prop="created_at" label="记录时间">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { getUserDetail, type User, type UserDetailResponse } from "@/api/users";

interface Props {
  modelValue: boolean;
  data?: User | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const statistics = ref<UserDetailResponse["data"]["statistics"] | null>(null);

// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return "-";
  return new Date(dateStr).toLocaleString("zh-CN");
};

// 加载用户详细统计数据
const loadUserStatistics = async (userId: string) => {
  loading.value = true;
  try {
    const response = await getUserDetail(userId);
    if (response.code === 200) {
      statistics.value = response.data.statistics;
    }
  } catch (error) {
    console.error("加载用户统计数据失败:", error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
    if (val && props.data) {
      // 对话框打开时自动加载统计数据
      statistics.value = null;
      loadUserStatistics(props.data.id);
    }
  },
  { immediate: true }
);

watch(visible, val => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}

.basic-info-section {
  margin-bottom: 24px;
}

.statistics-section {
  margin-top: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.loading-container {
  padding: 20px 0;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
